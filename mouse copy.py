from ctypes import Structure, WINFUNCTYPE, wintypes, windll, byref
from threading import get_ident
from signal import signal, SIGINT

WH_MOUSE_LL = 14
WM_MOUSEMOVE = 0x0200
WM_QUIT = 0x0012


class MSLLHOOKSTRUCT(Structure):
    _fields_ = [
        ('pt', wintypes.POINT),
        ('mouseData', wintypes.DWORD),
        ('flags', wintypes.DWORD),
        ('time', wintypes.DWORD),
        ('dwExtraInfo', wintypes.HANDLE)
    ]


_HOOKPROC = WINFUNCTYPE(
    wintypes.LPARAM,
    wintypes.INT,
    wintypes.WPARAM,
    wintypes.LPARAM
)
windll.user32.CallNextHookEx.argtypes = (
    wintypes.HHOOK,
    wintypes.INT,
    wintypes.WPARAM,
    wintypes.LPARAM
)


@_HOOKPROC
def mouse_hook(nCode, wParam, lParam):
    if nCode >= 0:
        mouse_struct = MSLLHOOKSTRUCT.from_address(lParam)
        x, y = mouse_struct.pt.x, mouse_struct.pt.y

        if wParam == WM_MOUSEMOVE:
            print(f'Mouse Move: ({x}, {y})')

    return windll.user32.CallNextHookEx(0, nCode, wParam, lParam)


def signal_handler(signum, _):
    if signum == SIGINT:
        windll.user32.PostThreadMessageW(get_ident(), WM_QUIT, 0, 0)


if __name__ == '__main__':
    signal(SIGINT, signal_handler)
    hook = windll.user32.SetWindowsHookExW(WH_MOUSE_LL, mouse_hook, None, 0)
    if not hook:
        print('SetWindowsHookEx failed')
        exit(1)
    print('Mouse hook installed, move mouse and click buttons...')

    windll.user32.GetMessageW(byref(wintypes.MSG()), None, 0, 0)
    windll.user32.UnhookWindowsHookEx(hook)
    print('Hook removed')
