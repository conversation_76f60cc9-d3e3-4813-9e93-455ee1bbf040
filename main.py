from time import time, sleep
from ctypes import Structure, WINFUNCTYPE, wintypes, windll, byref
from threading import Thread, Event
from sys import exit as sys_exit
from signal import signal, SIGINT, SIGTERM

WH_MOUSE_LL = 14
WM_MOUSEMOVE = 0x0200

MOVE_PERIOD = 2  # 单个方向的移动时长，单位：秒
MOVE_SPEED = 2  # 每次移动的像素
FPS = 30
STANDBY_PERIOD = 5
MAX_DIST = MOVE_SPEED * MOVE_PERIOD * FPS

GLOBAL_STAT = {
    'count_down': STANDBY_PERIOD,
    'signal': Event(),
    'prev_mouse_y': None,
    'last_mouse_x': None,
    'last_mouse_y': None,
    'hook': None
}


def thread_timer():
    while True:
        sleep(1 - time() % 1)
        print('timer', GLOBAL_STAT['count_down'])
        if GLOBAL_STAT['count_down'] > 0:
            GLOBAL_STAT['count_down'] -= 1
        else:
            if not GLOBAL_STAT['signal'].is_set():
                GLOBAL_STAT['signal'].set()


class MSLLHOOKSTRUCT(Structure):
    _fields_ = [
        ('pt', wintypes.POINT),
        ('mouseData', wintypes.DWORD),
        ('flags', wintypes.DWORD),
        ('time', wintypes.DWORD),
        ('dwExtraInfo', wintypes.HANDLE)
    ]


windll.user32.CallNextHookEx.argtypes = (
    wintypes.HHOOK,
    wintypes.INT,
    wintypes.WPARAM,
    wintypes.LPARAM
)


@WINFUNCTYPE(wintypes.LPARAM, wintypes.INT, wintypes.WPARAM, wintypes.LPARAM)
def mouse_hook(nCode, wParam, lParam):
    if nCode >= 0 and wParam == WM_MOUSEMOVE:
        mouse_struct = MSLLHOOKSTRUCT.from_address(lParam)
        if not GLOBAL_STAT['signal'].is_set():
            GLOBAL_STAT['prev_mouse_y'] = mouse_struct.pt.y
        if GLOBAL_STAT['last_mouse_x'] is not None and GLOBAL_STAT['last_mouse_x'] != mouse_struct.pt.x:
            if GLOBAL_STAT['signal'].is_set():
                GLOBAL_STAT['signal'].clear()
            GLOBAL_STAT['count_down'] = STANDBY_PERIOD
        GLOBAL_STAT['last_mouse_x'] = mouse_struct.pt.x
        GLOBAL_STAT['last_mouse_y'] = mouse_struct.pt.y

    return windll.user32.CallNextHookEx(0, nCode, wParam, lParam)


def thread_mouse_listener():
    _hook = windll.user32.SetWindowsHookExW(WH_MOUSE_LL, mouse_hook, None, 0)
    if not _hook:
        raise Exception('SetWindowsHookEx failed')
    GLOBAL_STAT['hook'] = _hook
    windll.user32.GetMessageW(byref(wintypes.MSG()), None, 0, 0)


def handle_termination(signum, _):
    if signum == SIGINT:
        if GLOBAL_STAT['hook']:
            windll.user32.UnhookWindowsHookEx(GLOBAL_STAT['hook'])
        sys_exit(0)


if __name__ == '__main__':
    signal(SIGINT, handle_termination)
    signal(SIGTERM, handle_termination)
    Thread(target=thread_timer, daemon=True).start()
    Thread(target=thread_mouse_listener, daemon=True).start()

    while True:
        GLOBAL_STAT['signal'].wait()
        _move = MOVE_SPEED * (int(time() // MOVE_PERIOD % 2) * 2 - 1)
        if (
            GLOBAL_STAT['prev_mouse_y'] is None
            or abs(GLOBAL_STAT['last_mouse_y'] + _move - GLOBAL_STAT['prev_mouse_y']) <= MAX_DIST
        ):
            windll.user32.mouse_event(0x0001, 0, _move, 0, 0)
        sleep(1 / FPS)
